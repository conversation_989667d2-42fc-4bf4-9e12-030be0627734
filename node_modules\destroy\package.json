{"_from": "destroy@1.2.0", "_id": "destroy@1.2.0", "_inBundle": false, "_integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==", "_location": "/destroy", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "destroy@1.2.0", "name": "destroy", "escapedName": "destroy", "rawSpec": "1.2.0", "saveSpec": null, "fetchSpec": "1.2.0"}, "_requiredBy": ["/body-parser", "/send"], "_resolved": "https://registry.npmmirror.com/destroy/-/destroy-1.2.0.tgz", "_shasum": "4803735509ad8be552934c67df614f94e66fa015", "_spec": "destroy@1.2.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\body-parser", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/stream-utils/destroy/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "destroy a stream if possible", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}, "files": ["index.js", "LICENSE"], "homepage": "https://github.com/stream-utils/destroy#readme", "keywords": ["stream", "streams", "destroy", "cleanup", "leak", "fd"], "license": "MIT", "name": "destroy", "repository": {"type": "git", "url": "git+https://github.com/stream-utils/destroy.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "1.2.0"}