{"_from": "vary@~1.1.2", "_id": "vary@1.1.2", "_inBundle": false, "_integrity": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==", "_location": "/vary", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vary@~1.1.2", "name": "vary", "escapedName": "vary", "rawSpec": "~1.1.2", "saveSpec": null, "fetchSpec": "~1.1.2"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz", "_shasum": "2299f02c6ded30d4a5961b0b9f74524a18f634fc", "_spec": "vary@~1.1.2", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/vary/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Manipulate the HTTP Vary header", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "5.1.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "2.5.3", "supertest": "1.1.0"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/jshttp/vary#readme", "keywords": ["http", "res", "vary"], "license": "MIT", "name": "vary", "repository": {"type": "git", "url": "git+https://github.com/jshttp/vary.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "1.1.2"}