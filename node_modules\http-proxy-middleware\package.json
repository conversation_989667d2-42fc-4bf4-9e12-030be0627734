{"_from": "http-proxy-middleware@^2.0.6", "_id": "http-proxy-middleware@2.0.9", "_inBundle": false, "_integrity": "sha512-c1IyJYLYppU574+YI7R4QyX2ystMtVXZwIdzazUIPIJsHuWNd+mho2j+bKoHftndicGj9yh+xjd+l0yj7VeT1Q==", "_location": "/http-proxy-middleware", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "http-proxy-middleware@^2.0.6", "name": "http-proxy-middleware", "escapedName": "http-proxy-middleware", "rawSpec": "^2.0.6", "saveSpec": null, "fetchSpec": "^2.0.6"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/http-proxy-middleware/-/http-proxy-middleware-2.0.9.tgz", "_shasum": "e9e63d68afaa4eee3d147f39149ab84c0c2815ef", "_spec": "http-proxy-middleware@^2.0.6", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/chimurai/http-proxy-middleware/issues"}, "bundleDependencies": false, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}, "deprecated": false, "description": "The one-liner node.js proxy middleware for connect, express and browser-sync", "devDependencies": {"@commitlint/cli": "16.2.1", "@commitlint/config-conventional": "16.2.1", "@types/express": "4.17.13", "@types/is-glob": "4.0.2", "@types/jest": "27.4.0", "@types/micromatch": "4.0.2", "@types/node": "17.0.18", "@types/supertest": "2.0.11", "@types/ws": "8.2.2", "@typescript-eslint/eslint-plugin": "5.12.0", "@typescript-eslint/parser": "5.12.0", "body-parser": "1.19.2", "browser-sync": "2.27.7", "connect": "3.7.0", "eslint": "8.9.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "4.0.0", "express": "4.17.3", "get-port": "5.1.1", "husky": "7.0.4", "jest": "27.5.1", "lint-staged": "12.3.4", "mockttp": "2.6.0", "open": "8.4.0", "prettier": "2.5.1", "supertest": "6.2.2", "ts-jest": "27.1.3", "typescript": "4.5.5", "ws": "8.5.0"}, "engines": {"node": ">=12.0.0"}, "files": ["dist"], "homepage": "https://github.com/chimurai/http-proxy-middleware#readme", "keywords": ["reverse", "proxy", "middleware", "http", "https", "connect", "express", "fastify", "polka", "browser-sync", "gulp", "grunt-contrib-connect", "websocket", "ws", "cors"], "license": "MIT", "main": "dist/index.js", "name": "http-proxy-middleware", "peerDependencies": {"@types/express": "^4.17.13"}, "peerDependenciesMeta": {"@types/express": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/chimurai/http-proxy-middleware.git"}, "scripts": {"build": "tsc", "clean": "rm -rf dist && rm -rf coverage", "coverage": "jest --coverage --coverageReporters=lcov", "eslint": "eslint '{src,test}/**/*.ts'", "eslint:fix": "yarn eslint --fix", "lint": "yarn prettier && yarn eslint", "lint:fix": "yarn prettier:fix && yarn eslint:fix", "prebuild": "yarn clean", "precoverage": "yarn build", "prepack": "yarn build && rm dist/tsconfig.tsbuildinfo", "prepare": "husky install", "pretest": "yarn build", "prettier": "prettier --list-different \"**/*.{js,ts,md,yml,json,html}\"", "prettier:fix": "prettier --write \"**/*.{js,ts,md,yml,json,html}\"", "spellcheck": "npx --yes cspell --show-context --show-suggestions '**/*.*'", "test": "jest"}, "types": "dist/index.d.ts", "version": "2.0.9"}