{"_from": "content-type@~1.0.4", "_id": "content-type@1.0.5", "_inBundle": false, "_integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==", "_location": "/content-type", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "content-type@~1.0.4", "name": "content-type", "escapedName": "content-type", "rawSpec": "~1.0.4", "saveSpec": null, "fetchSpec": "~1.0.4"}, "_requiredBy": ["/body-parser", "/express"], "_resolved": "https://registry.npmmirror.com/content-type/-/content-type-1.0.5.tgz", "_shasum": "8b773162656d1d1086784c8f23a54ce6d73d7918", "_spec": "content-type@~1.0.4", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/content-type/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Create and parse HTTP Content-Type header", "devDependencies": {"deep-equal": "1.0.1", "eslint": "8.32.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "10.2.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/content-type#readme", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "license": "MIT", "name": "content-type", "repository": {"type": "git", "url": "git+https://github.com/jshttp/content-type.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "1.0.5"}