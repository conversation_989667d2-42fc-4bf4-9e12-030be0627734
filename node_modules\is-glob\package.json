{"_from": "is-glob@^4.0.1", "_id": "is-glob@4.0.3", "_inBundle": false, "_integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "_location": "/is-glob", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-glob@^4.0.1", "name": "is-glob", "escapedName": "is-glob", "rawSpec": "^4.0.1", "saveSpec": null, "fetchSpec": "^4.0.1"}, "_requiredBy": ["/http-proxy-middleware"], "_resolved": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "_shasum": "64f61e42cbbb2eec2071a9dac0b28ba1e65d5084", "_spec": "is-glob@^4.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-proxy-middleware", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/is-glob/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "https://tuvistavie.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "dependencies": {"is-extglob": "^2.1.1"}, "deprecated": false, "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a better user experience.", "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/micromatch/is-glob", "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "license": "MIT", "main": "index.js", "name": "is-glob", "repository": {"type": "git", "url": "git+https://github.com/micromatch/is-glob.git"}, "scripts": {"test": "mocha && node benchmark.js"}, "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["assemble", "base", "update", "verb"]}, "reflinks": ["assemble", "bach", "base", "composer", "gulp", "has-glob", "is-valid-glob", "micromatch", "npm", "scaffold", "verb", "vinyl"]}, "version": "4.0.3"}