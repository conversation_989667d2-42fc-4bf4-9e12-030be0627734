{"_from": "inherits@2.0.4", "_id": "inherits@2.0.4", "_inBundle": false, "_integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "_location": "/inherits", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "inherits@2.0.4", "name": "inherits", "escapedName": "inherits", "rawSpec": "2.0.4", "saveSpec": null, "fetchSpec": "2.0.4"}, "_requiredBy": ["/http-errors"], "_resolved": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "_shasum": "0fa2c64f932917c3433a0ded55363aae37416b7c", "_spec": "inherits@2.0.4", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-errors", "browser": "./inherits_browser.js", "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "devDependencies": {"tap": "^14.2.4"}, "files": ["inherits.js", "inherits_browser.js"], "homepage": "https://github.com/isaacs/inherits#readme", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "license": "ISC", "main": "./inherits.js", "name": "inherits", "repository": {"type": "git", "url": "git://github.com/isaacs/inherits.git"}, "scripts": {"test": "tap"}, "version": "2.0.4"}