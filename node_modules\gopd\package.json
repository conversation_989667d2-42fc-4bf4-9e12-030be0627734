{"_from": "gopd@^1.2.0", "_id": "gopd@1.2.0", "_inBundle": false, "_integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "_location": "/gopd", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "gopd@^1.2.0", "name": "gopd", "escapedName": "gopd", "rawSpec": "^1.2.0", "saveSpec": null, "fetchSpec": "^1.2.0"}, "_requiredBy": ["/dunder-proto", "/get-intrinsic"], "_resolved": "https://registry.npmmirror.com/gopd/-/gopd-1.2.0.tgz", "_shasum": "89f56b8217bdbc8802bd299df6d7f1081d7e51a1", "_spec": "gopd@^1.2.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\get-intrinsic", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/gopd/issues"}, "bundleDependencies": false, "deprecated": false, "description": "`Object.getOwnPropertyDescriptor`, but accounts for IE's broken implementation.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./gOPD": "./gOPD.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/gopd#readme", "keywords": ["ecmascript", "javascript", "getownpropertydescriptor", "property", "descriptor"], "license": "MIT", "main": "index.js", "name": "gopd", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/gopd.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "evalmd README.md", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "tsc -p . && attw -P", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "1.2.0"}