{"_from": "is-number@^7.0.0", "_id": "is-number@7.0.0", "_inBundle": false, "_integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "_location": "/is-number", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-number@^7.0.0", "name": "is-number", "escapedName": "is-number", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}, "_requiredBy": ["/to-regex-range"], "_resolved": "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz", "_shasum": "7535345b896734d5f80c4d06c50955527a14f12b", "_spec": "is-number@^7.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://i.am.charlike.online"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "deprecated": false, "description": "Returns true if a number or string value is a finite number. Useful for regex matches, parsing, user input, etc.", "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "engines": {"node": ">=0.12.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-number", "keywords": ["cast", "check", "coerce", "coercion", "finite", "integer", "is", "isnan", "is-nan", "is-num", "is-number", "isnumber", "isfinite", "istype", "kind", "math", "nan", "num", "number", "numeric", "parseFloat", "parseInt", "test", "type", "typeof", "value"], "license": "MIT", "main": "index.js", "name": "is-number", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-number.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "related": {"list": ["is-plain-object", "is-primitive", "isobject", "kind-of"]}, "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "7.0.0"}