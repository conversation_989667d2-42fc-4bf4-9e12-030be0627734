{"_from": "toidentifier@1.0.1", "_id": "toidentifier@1.0.1", "_inBundle": false, "_integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==", "_location": "/toidentifier", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "toidentifier@1.0.1", "name": "toidentifier", "escapedName": "toidentifier", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/http-errors"], "_resolved": "https://registry.npmmirror.com/toidentifier/-/toidentifier-1.0.1.tgz", "_shasum": "3be34321a88a820ed1bd80dfaa33e479fbb8dd35", "_spec": "toidentifier@1.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-errors", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/component/toidentifier/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://niftylettuce.com/"}], "deprecated": false, "description": "Convert a string of words to a JavaScript identifier", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.3", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "9.1.3", "nyc": "15.1.0"}, "engines": {"node": ">=0.6"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "homepage": "https://github.com/component/toidentifier#readme", "license": "MIT", "name": "toidentifier", "repository": {"type": "git", "url": "git+https://github.com/component/toidentifier.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "1.0.1"}