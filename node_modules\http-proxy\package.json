{"_from": "http-proxy@^1.18.1", "_id": "http-proxy@1.18.1", "_inBundle": false, "_integrity": "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==", "_location": "/http-proxy", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "http-proxy@^1.18.1", "name": "http-proxy", "escapedName": "http-proxy", "rawSpec": "^1.18.1", "saveSpec": null, "fetchSpec": "^1.18.1"}, "_requiredBy": ["/http-proxy-middleware"], "_resolved": "https://registry.npmmirror.com/http-proxy/-/http-proxy-1.18.1.tgz", "_shasum": "401541f0534884bbf95260334e72f88ee3976549", "_spec": "http-proxy@^1.18.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-proxy-middleware", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/http-party/node-http-proxy/issues"}, "bundleDependencies": false, "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "deprecated": false, "description": "HTTP proxying for the masses", "devDependencies": {"async": "^3.0.0", "auto-changelog": "^1.15.0", "concat-stream": "^2.0.0", "expect.js": "~0.3.1", "mocha": "^3.5.3", "nyc": "^14.0.0", "semver": "^5.0.3", "socket.io": "^2.1.0", "socket.io-client": "^2.1.0", "sse": "0.0.8", "ws": "^3.0.0"}, "engines": {"node": ">=8.0.0"}, "homepage": "https://github.com/http-party/node-http-proxy#readme", "license": "MIT", "main": "index.js", "maintainers": [{"name": "jcrugzz", "email": "<EMAIL>"}], "name": "http-proxy", "repository": {"type": "git", "url": "git+https://github.com/http-party/node-http-proxy.git"}, "scripts": {"mocha": "mocha test/*-test.js", "test": "nyc --reporter=text --reporter=lcov npm run mocha", "version": "auto-changelog -p && git add CHANGELOG.md"}, "version": "1.18.1"}