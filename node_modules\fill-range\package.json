{"_from": "fill-range@^7.1.1", "_id": "fill-range@7.1.1", "_inBundle": false, "_integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "_location": "/fill-range", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "fill-range@^7.1.1", "name": "fill-range", "escapedName": "fill-range", "rawSpec": "^7.1.1", "saveSpec": null, "fetchSpec": "^7.1.1"}, "_requiredBy": ["/braces"], "_resolved": "https://registry.npmmirror.com/fill-range/-/fill-range-7.1.1.tgz", "_shasum": "44265d3cac07e3ea7dc247516380643754a05292", "_spec": "fill-range@^7.1.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\braces", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/fill-range/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "edo.rivai.nl"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "paulmillr.com"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "dependencies": {"to-regex-range": "^5.0.1"}, "deprecated": false, "description": "Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`", "devDependencies": {"gulp-format-md": "^2.0.0", "mocha": "^6.1.1", "nyc": "^15.1.0"}, "engines": {"node": ">=8"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/fill-range", "keywords": ["alpha", "alphabetical", "array", "bash", "brace", "expand", "expansion", "fill", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sh"], "license": "MIT", "main": "index.js", "name": "fill-range", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/fill-range.git"}, "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "mocha": "mocha --reporter dot", "test": "npm run lint && npm run mocha", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "7.1.1"}