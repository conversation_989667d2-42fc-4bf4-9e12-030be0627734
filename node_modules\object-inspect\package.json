{"_from": "object-inspect@^1.13.3", "_id": "object-inspect@1.13.4", "_inBundle": false, "_integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==", "_location": "/object-inspect", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "object-inspect@^1.13.3", "name": "object-inspect", "escapedName": "object-inspect", "rawSpec": "^1.13.3", "saveSpec": null, "fetchSpec": "^1.13.3"}, "_requiredBy": ["/side-channel", "/side-channel-list", "/side-channel-map", "/side-channel-weakmap"], "_resolved": "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.13.4.tgz", "_shasum": "8375265e21bc20d0fa582c22e1b13485d6e00213", "_spec": "object-inspect@^1.13.3", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\side-channel", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "browser": {"./util.inspect.js": false}, "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "bundleDependencies": false, "deprecated": false, "description": "string representations of objects in node and the browser", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "@pkgjs/support": "^0.0.6", "auto-changelog": "^2.5.0", "core-js": "^2.6.12", "error-cause": "^1.0.8", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "for-each": "^0.3.4", "functions-have-names": "^1.2.3", "glob": "=10.3.7", "globalthis": "^1.0.4", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "in-publish": "^2.0.1", "jackspeak": "=2.1.1", "make-arrow-function": "^1.2.0", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "safer-buffer": "^2.1.2", "semver": "^6.3.1", "string.prototype.repeat": "^1.0.0", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/object-inspect", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "license": "MIT", "main": "index.js", "name": "object-inspect", "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/object-inspect.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "npx @pkgjs/support validate", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:corejs", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "tests-only": "nyc tape 'test/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "support": true, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "version": "1.13.4"}