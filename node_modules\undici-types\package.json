{"_from": "undici-types@~7.10.0", "_id": "undici-types@7.10.0", "_inBundle": false, "_integrity": "sha512-t5Fy/nfn+14LuOc2KNYg75vZqClpAiqscVvMygNnlsHBFpSXdJaYtXMcdNLpl/Qvc3P2cB3s6lOV51nqsFq4ag==", "_location": "/undici-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "undici-types@~7.10.0", "name": "undici-types", "escapedName": "undici-types", "rawSpec": "~7.10.0", "saveSpec": null, "fetchSpec": "~7.10.0"}, "_requiredBy": ["/@types/node"], "_resolved": "https://registry.npmmirror.com/undici-types/-/undici-types-7.10.0.tgz", "_shasum": "4ac2e058ce56b462b056e629cc6a02393d3ff350", "_spec": "undici-types@~7.10.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\@types\\node", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/dnlup"}, {"name": "<PERSON>", "url": "https://github.com/ethan-arrowood"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/KhafraDev"}, {"name": "<PERSON>", "url": "https://github.com/ronag"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/szmarczak"}, {"name": "<PERSON>", "url": "https://github.com/delvedor"}], "deprecated": false, "description": "A stand-alone types package for Undici", "files": ["*.d.ts"], "homepage": "https://undici.nodejs.org", "license": "MIT", "name": "undici-types", "repository": {"type": "git", "url": "git+https://github.com/nodejs/undici.git"}, "types": "index.d.ts", "version": "7.10.0"}