{"_from": "is-plain-obj@^3.0.0", "_id": "is-plain-obj@3.0.0", "_inBundle": false, "_integrity": "sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==", "_location": "/is-plain-obj", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-plain-obj@^3.0.0", "name": "is-plain-obj", "escapedName": "is-plain-obj", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/http-proxy-middleware"], "_resolved": "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-3.0.0.tgz", "_shasum": "af6f2ea14ac5a646183a5bbdb5baabbc156ad9d7", "_spec": "is-plain-obj@^3.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-proxy-middleware", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-plain-obj/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if a value is a plain object", "devDependencies": {"ava": "^2.4.0", "tsd": "^0.13.1", "xo": "^0.33.1"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/is-plain-obj#readme", "keywords": ["object", "is", "check", "test", "type", "plain", "vanilla", "pure", "simple"], "license": "MIT", "name": "is-plain-obj", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-plain-obj.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.0.0"}