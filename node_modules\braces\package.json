{"_from": "braces@^3.0.3", "_id": "braces@3.0.3", "_inBundle": false, "_integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "_location": "/braces", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "braces@^3.0.3", "name": "braces", "escapedName": "braces", "rawSpec": "^3.0.3", "saveSpec": null, "fetchSpec": "^3.0.3"}, "_requiredBy": ["/micromatch"], "_resolved": "https://registry.npmmirror.com/braces/-/braces-3.0.3.tgz", "_shasum": "490332f40919452272d55a8480adc0c441358789", "_spec": "braces@^3.0.3", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "url": "https://github.com/eush77"}, {"name": "hemanth.hm", "url": "http://h3manth.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "dependencies": {"fill-range": "^7.1.1"}, "deprecated": false, "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "devDependencies": {"ansi-colors": "^3.2.4", "bash-path": "^2.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.1.1"}, "engines": {"node": ">=8"}, "files": ["index.js", "lib"], "homepage": "https://github.com/micromatch/braces", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "license": "MIT", "main": "index.js", "name": "braces", "repository": {"type": "git", "url": "git+https://github.com/micromatch/braces.git"}, "scripts": {"benchmark": "node benchmark", "test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "lint": {"reflinks": true}, "plugins": ["gulp-format-md"]}, "version": "3.0.3"}