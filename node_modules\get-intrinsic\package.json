{"_from": "get-intrinsic@^1.2.5", "_id": "get-intrinsic@1.3.0", "_inBundle": false, "_integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "_location": "/get-intrinsic", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "get-intrinsic@^1.2.5", "name": "get-intrinsic", "escapedName": "get-intrinsic", "rawSpec": "^1.2.5", "saveSpec": null, "fetchSpec": "^1.2.5"}, "_requiredBy": ["/call-bound", "/side-channel-map", "/side-channel-weakmap"], "_resolved": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "_shasum": "743f0e3b6964a93a5491ed1bffaae054d7f98d01", "_spec": "get-intrinsic@^1.2.5", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\side-channel-map", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "bundleDependencies": false, "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "deprecated": false, "description": "Get and robustly cache all JS language-level intrinsics at first require time", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "call-bound": "^1.0.3", "encoding": "^0.1.13", "es-abstract": "^1.23.9", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/get-intrinsic#readme", "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "license": "MIT", "main": "index.js", "name": "get-intrinsic", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/get-intrinsic.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/GetIntrinsic.js"}, "version": "1.3.0"}