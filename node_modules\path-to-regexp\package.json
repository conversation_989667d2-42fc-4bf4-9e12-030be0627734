{"_from": "path-to-regexp@0.1.12", "_id": "path-to-regexp@0.1.12", "_inBundle": false, "_integrity": "sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ==", "_location": "/path-to-regexp", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "path-to-regexp@0.1.12", "name": "path-to-regexp", "escapedName": "path-to-regexp", "rawSpec": "0.1.12", "saveSpec": null, "fetchSpec": "0.1.12"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-0.1.12.tgz", "_shasum": "d5e1a12e478a976d432ef3c58d534b9923164bb7", "_spec": "path-to-regexp@0.1.12", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "bundleDependencies": false, "component": {"scripts": {"path-to-regexp": "index.js"}}, "deprecated": false, "description": "Express style path to RegExp utility", "devDependencies": {"istanbul": "^0.2.6", "mocha": "^1.17.1"}, "files": ["index.js", "LICENSE"], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "keywords": ["express", "regexp"], "license": "MIT", "name": "path-to-regexp", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/path-to-regexp.git"}, "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "version": "0.1.12"}