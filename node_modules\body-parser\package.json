{"_from": "body-parser@1.20.3", "_id": "body-parser@1.20.3", "_inBundle": false, "_integrity": "sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==", "_location": "/body-parser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "body-parser@1.20.3", "name": "body-parser", "escapedName": "body-parser", "rawSpec": "1.20.3", "saveSpec": null, "fetchSpec": "1.20.3"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/body-parser/-/body-parser-1.20.3.tgz", "_shasum": "1953431221c6fb5cd63c4b36d53fab0928e548c6", "_spec": "body-parser@1.20.3", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "deprecated": false, "description": "Node.js body parsing middleware", "devDependencies": {"eslint": "8.34.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "methods": "1.1.2", "mocha": "10.2.0", "nyc": "15.1.0", "safe-buffer": "5.2.1", "supertest": "6.3.3"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}, "files": ["lib/", "LICENSE", "HISTORY.md", "SECURITY.md", "index.js"], "homepage": "https://github.com/expressjs/body-parser#readme", "license": "MIT", "name": "body-parser", "repository": {"type": "git", "url": "git+https://github.com/expressjs/body-parser.git"}, "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "1.20.3"}